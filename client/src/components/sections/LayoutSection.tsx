import { motion } from 'framer-motion';
import {
  AlertTriangle,
  Clock,
  DollarSign,
  Puzzle,
  Shield,
  TrendingDown,
  Users,
} from 'lucide-react';

export const LayoutSection = () => {
  const problemCards = [
    {
      title: 'Cash Flow Constraints',
      description:
        'Immediate payment needs drain liquidity, limiting flexibility for growth and operations.',
      icon: TrendingDown,
    },
    {
      title: 'Payment Delays',
      description:
        'Slow payment cycles stall critical transactions and disrupt business continuity.',
      icon: Clock,
    },
    {
      title: 'Manual Workflows',
      description: 'Legacy processes lead to errors, delays, and increased operational effort.',
      icon: AlertTriangle,
    },
    {
      title: 'Hidden Costs',
      description:
        'Traditional methods often include opaque fees, poor visibility, and inefficient use of capital.',
      icon: DollarSign,
    },
    {
      title: 'Supplier Friction',
      description:
        'Lack of payment flexibility weakens vendor relationships and reduces negotiation leverage.',
      icon: Users,
    },
    {
      title: 'Disconnected Systems',
      description:
        'Fragmented finance tools cause inefficiencies and limit real-time decision-making.',
      icon: Puzzle,
    },
    {
      title: 'Security Gaps',
      description: 'Inadequate controls expose payments to fraud, errors, and compliance risks.',
      icon: Shield,
    },
  ];

  return (
    <section className="section-spacing bg-gradient-to-br from-slate-50/80 via-red-50/40 to-orange-50/60 relative overflow-hidden">
      {/* Enhanced Background Elements */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute top-10 right-20 w-72 h-72 bg-gradient-to-br from-red-100/60 to-orange-100/60 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 left-10 w-64 h-64 bg-gradient-to-br from-purple-100/40 to-pink-100/40 rounded-full blur-3xl"></div>
      </div>

      {/* Subtle grid pattern overlay */}
      <div
        className="absolute inset-0 opacity-[0.02]"
        style={{
          backgroundImage: `radial-gradient(circle at 1px 1px, rgb(51 65 85) 1px, transparent 0)`,
          backgroundSize: '24px 24px',
        }}
      ></div>

      {/* Aligned with Hero section margins for perfect consistency */}
      <div className="max-w-none px-1 sm:px-2 lg:px-3 xl:px-4 w-full">
        <div className="w-full max-w-[98%] xl:max-w-[96%] mx-auto relative">
          {/* Header */}
          <div className="text-center mb-8 sm:mb-10 md:mb-12 lg:mb-14">
            <h2 className="text-responsive-xl font-bold text-slate-900 mb-4 sm:mb-6 leading-tight">
              Traditional B2B payouts fail —
              <span className="bg-gradient-to-r from-purple-600 via-blue-500 to-indigo-600 bg-clip-text text-transparent">
                {' '}
                and what we fix.
              </span>
            </h2>
          </div>

          {/* Centered Dynamic Problem Visualization */}
          <div className="flex items-center justify-center mb-8 sm:mb-10 lg:mb-12">
            <motion.div
              className="relative flex items-center justify-center"
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, ease: 'easeOut' }}
            >
              <div className="relative w-64 h-64 sm:w-72 sm:h-72 md:w-80 md:h-80 lg:w-96 lg:h-96">
                {/* Central Problem Core */}
                <motion.div
                  className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 sm:w-36 sm:h-36 md:w-40 md:h-40 lg:w-44 lg:h-44 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center shadow-2xl border-4 border-white"
                  animate={{
                    scale: [1, 1.05, 1],
                    boxShadow: [
                      '0 25px 50px -12px rgba(239, 68, 68, 0.25)',
                      '0 25px 50px -12px rgba(239, 68, 68, 0.4)',
                      '0 25px 50px -12px rgba(239, 68, 68, 0.25)',
                    ],
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: 'easeInOut',
                  }}
                >
                  <div className="text-center">
                    <AlertTriangle className="w-8 h-8 sm:w-9 sm:h-9 md:w-10 md:h-10 lg:w-11 lg:h-11 text-white mx-auto mb-1" />
                    <p className="text-sm sm:text-base md:text-lg lg:text-xl font-bold text-white">
                      Legacy
                    </p>
                    <p className="text-sm sm:text-base md:text-lg lg:text-xl font-bold text-white">
                      Issues
                    </p>
                  </div>
                </motion.div>

                {/* Orbiting Problem Indicators */}
                {[0, 1, 2, 3, 4].map(index => {
                  // Calculate angle for perfect pentagon positioning
                  const angleInDegrees = -90 + index * 72; // Start from top, 72° intervals for pentagon
                  const angleInRadians = angleInDegrees * (Math.PI / 180);

                  // Responsive radius calculation for perfect positioning
                  const baseRadius = 128; // Base radius in pixels for lg screens
                  const radiusResponsive = {
                    sm: baseRadius * 0.75, // 96px
                    md: baseRadius * 0.875, // 112px
                    lg: baseRadius, // 128px
                  };

                  // Calculate positions for each breakpoint
                  const positions = {
                    sm: {
                      x: 50 + ((Math.cos(angleInRadians) * radiusResponsive.sm) / 288) * 100, // 288 = w-72
                      y: 50 + ((Math.sin(angleInRadians) * radiusResponsive.sm) / 288) * 100,
                    },
                    md: {
                      x: 50 + ((Math.cos(angleInRadians) * radiusResponsive.md) / 320) * 100, // 320 = w-80
                      y: 50 + ((Math.sin(angleInRadians) * radiusResponsive.md) / 320) * 100,
                    },
                    lg: {
                      x: 50 + ((Math.cos(angleInRadians) * radiusResponsive.lg) / 384) * 100, // 384 = w-96
                      y: 50 + ((Math.sin(angleInRadians) * radiusResponsive.lg) / 384) * 100,
                    },
                  };

                  return (
                    <motion.div
                      key={index}
                      className="absolute w-6 h-6 sm:w-7 sm:h-7 md:w-8 md:h-8 lg:w-9 lg:h-9 bg-gradient-to-br from-orange-400 to-red-500 rounded-full shadow-lg border-2 border-white"
                      style={{
                        // Use CSS custom properties for responsive positioning
                        left: `${positions.lg.x}%`,
                        top: `${positions.lg.y}%`,
                        transform: 'translate(-50%, -50%)',
                      }}
                      animate={{
                        scale: [1, 1.3, 1],
                        opacity: [0.8, 1, 0.8],
                      }}
                      transition={{
                        duration: 2.5,
                        repeat: Infinity,
                        delay: index * 0.4,
                        ease: 'easeInOut',
                      }}
                    />
                  );
                })}

                {/* Rotating Problem Ring */}
                <motion.div
                  className="absolute inset-0"
                  animate={{ rotate: 360 }}
                  transition={{
                    duration: 25,
                    repeat: Infinity,
                    ease: 'linear',
                  }}
                >
                  <svg className="w-full h-full" viewBox="0 0 384 384">
                    <circle
                      cx="192"
                      cy="192"
                      r="128"
                      fill="none"
                      stroke="url(#problemGradient)"
                      strokeWidth="3"
                      strokeDasharray="12 6"
                      opacity="0.6"
                    />
                    <defs>
                      <linearGradient id="problemGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stopColor="#ef4444" />
                        <stop offset="50%" stopColor="#f97316" />
                        <stop offset="100%" stopColor="#ef4444" />
                      </linearGradient>
                    </defs>
                  </svg>
                </motion.div>

                {/* Secondary Orbit Ring */}
                <motion.div
                  className="absolute inset-0"
                  animate={{ rotate: -360 }}
                  transition={{
                    duration: 35,
                    repeat: Infinity,
                    ease: 'linear',
                  }}
                >
                  <svg className="w-full h-full" viewBox="0 0 384 384">
                    <circle
                      cx="192"
                      cy="192"
                      r="160"
                      fill="none"
                      stroke="url(#problemGradientOuter)"
                      strokeWidth="2"
                      strokeDasharray="8 12"
                      opacity="0.3"
                    />
                    <defs>
                      <linearGradient id="problemGradientOuter" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stopColor="#f97316" />
                        <stop offset="100%" stopColor="#ef4444" />
                      </linearGradient>
                    </defs>
                  </svg>
                </motion.div>

                {/* Problem Label */}
                <motion.div
                  className="absolute -top-8 sm:-top-9 md:-top-10 lg:-top-12 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-red-500 to-orange-500 text-white px-4 py-2 sm:px-5 sm:py-2.5 md:px-6 md:py-3 lg:px-7 lg:py-3.5 rounded-full font-semibold text-sm sm:text-base md:text-lg lg:text-xl shadow-xl whitespace-nowrap border-2 border-white"
                  initial={{ opacity: 0, y: -20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: 1 }}
                >
                  The Problem
                </motion.div>
              </div>
            </motion.div>
          </div>

          {/* Enterprise-Grade 2x2+1 Card Grid */}
          <div className="space-y-6 sm:space-y-8">
            {/* First 6 cards in 2x2 grid (3 rows of 2 cards) */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-5 lg:gap-6">
              {problemCards.slice(0, 6).map((card, index) => (
                <motion.div
                  key={index}
                  className="group relative bg-white border border-slate-200/60 rounded-lg sm:rounded-xl p-3 sm:p-4 lg:p-5 shadow-sm hover:shadow-xl hover:shadow-red-500/10 hover:border-red-300/50 transition-all duration-500 hover:-translate-y-1 backdrop-blur-sm"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  whileHover={{ scale: 1.02 }}
                >
                  {/* Subtle gradient overlay on hover */}
                  <div className="absolute inset-0 bg-gradient-to-br from-red-50/0 via-orange-50/0 to-red-50/0 group-hover:from-red-50/30 group-hover:via-orange-50/20 group-hover:to-red-50/30 rounded-xl sm:rounded-2xl transition-all duration-500 opacity-0 group-hover:opacity-100" />

                  <div className="relative flex flex-col h-full">
                    <div className="flex items-start gap-2 sm:gap-3 mb-2 sm:mb-3">
                      <div className="flex-shrink-0 w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-slate-100 to-slate-200 rounded-lg flex items-center justify-center group-hover:from-red-100 group-hover:to-orange-100 group-hover:shadow-lg transition-all duration-500 group-hover:scale-110">
                        <card.icon className="w-4 h-4 sm:w-5 sm:h-5 text-slate-600 group-hover:text-red-600 transition-all duration-500" />
                      </div>
                      <h3 className="font-bold text-base sm:text-lg text-slate-900 group-hover:text-red-700 transition-all duration-500 pt-1 leading-tight">
                        {card.title}
                      </h3>
                    </div>
                    <p className="text-slate-600 group-hover:text-slate-700 leading-relaxed text-xs sm:text-sm transition-all duration-500">
                      {card.description}
                    </p>

                    {/* Subtle bottom accent line */}
                    <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-red-500 via-orange-500 to-red-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 rounded-b-xl sm:rounded-b-2xl" />
                  </div>
                </motion.div>
              ))}
            </div>

            {/* 7th card centered below the grid */}
            {problemCards.length > 6 && (
              <div className="flex justify-center">
                <div className="w-full max-w-md md:max-w-lg">
                  <motion.div
                    className="group relative bg-white border border-slate-200/60 rounded-lg sm:rounded-xl p-3 sm:p-4 lg:p-5 shadow-sm hover:shadow-xl hover:shadow-red-500/10 hover:border-red-300/50 transition-all duration-500 hover:-translate-y-1 backdrop-blur-sm"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 0.6 }}
                    whileHover={{ scale: 1.02 }}
                  >
                    {/* Subtle gradient overlay on hover */}
                    <div className="absolute inset-0 bg-gradient-to-br from-red-50/0 via-orange-50/0 to-red-50/0 group-hover:from-red-50/30 group-hover:via-orange-50/20 group-hover:to-red-50/30 rounded-xl sm:rounded-2xl transition-all duration-500 opacity-0 group-hover:opacity-100" />

                    <div className="relative flex flex-col h-full">
                      <div className="flex items-start gap-2 sm:gap-3 mb-2 sm:mb-3">
                        <div className="flex-shrink-0 w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-slate-100 to-slate-200 rounded-lg flex items-center justify-center group-hover:from-red-100 group-hover:to-orange-100 group-hover:shadow-lg transition-all duration-500 group-hover:scale-110">
                          {(() => {
                            const IconComponent = problemCards[6].icon;
                            return (
                              <IconComponent className="w-4 h-4 sm:w-5 sm:h-5 text-slate-600 group-hover:text-red-600 transition-all duration-500" />
                            );
                          })()}
                        </div>
                        <h3 className="font-bold text-base sm:text-lg text-slate-900 group-hover:text-red-700 transition-all duration-500 pt-1 leading-tight">
                          {problemCards[6].title}
                        </h3>
                      </div>
                      <p className="text-slate-600 group-hover:text-slate-700 leading-relaxed text-xs sm:text-sm transition-all duration-500">
                        {problemCards[6].description}
                      </p>

                      {/* Subtle bottom accent line */}
                      <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-red-500 via-orange-500 to-red-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 rounded-b-xl sm:rounded-b-2xl" />
                    </div>
                  </motion.div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};
